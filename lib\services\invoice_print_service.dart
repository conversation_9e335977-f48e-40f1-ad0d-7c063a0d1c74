import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../models/item.dart';
import '../services/customer_service.dart';
import '../services/supplier_service.dart';
import '../services/item_service.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../constants/app_constants.dart';
import '../constants/app_colors.dart';

/// خدمة طباعة الفواتير الاحترافية
/// توفر إنشاء وطباعة الفواتير بتنسيق PDF احترافي
class InvoicePrintService {
  static const String _companyName = 'دفتر الأستاذ الذكي';
  static const String _companyAddress = 'سوريا - دمشق';
  static const String _companyPhone = '+963-11-1234567';
  static const String _companyEmail = '<EMAIL>';

  static final CustomerService _customerService = CustomerService();
  static final SupplierService _supplierService = SupplierService();
  static final ItemService _itemService = ItemService();

  /// طباعة الفاتورة مع معاينة
  static Future<void> printInvoiceWithPreview(
    BuildContext context,
    Invoice invoice,
  ) async {
    try {
      LoggingService.info(
        'بدء طباعة الفاتورة مع معاينة',
        category: 'InvoicePrint',
        data: {'invoiceId': invoice.id, 'invoiceNumber': invoice.invoiceNumber},
      );

      final pdf = await _generateInvoicePDF(invoice);

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'فاتورة_${invoice.invoiceNumber}.pdf',
        format: PdfPageFormat.a4,
      );

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntityInvoice,
        entityId: invoice.id ?? 0,
        entityName: 'طباعة فاتورة ${invoice.invoiceNumber}',
        newValues: {
          'invoiceNumber': invoice.invoiceNumber,
          'printDate': DateTime.now().toIso8601String(),
          'printType': 'preview',
        },
        description: 'تم طباعة الفاتورة مع معاينة',
        category: 'InvoicePrint',
      );

      LoggingService.info(
        'تم طباعة الفاتورة بنجاح',
        category: 'InvoicePrint',
        data: {'invoiceNumber': invoice.invoiceNumber},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في طباعة الفاتورة',
        category: 'InvoicePrint',
        data: {'error': e.toString(), 'invoiceId': invoice.id},
      );
      rethrow;
    }
  }

  /// حفظ الفاتورة كملف PDF
  static Future<String?> saveInvoiceAsPDF(Invoice invoice) async {
    try {
      LoggingService.info(
        'بدء حفظ الفاتورة كـ PDF',
        category: 'InvoicePrint',
        data: {'invoiceId': invoice.id, 'invoiceNumber': invoice.invoiceNumber},
      );

      final pdf = await _generateInvoicePDF(invoice);
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'فاتورة_${invoice.invoiceNumber}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntityInvoice,
        entityId: invoice.id ?? 0,
        entityName: 'حفظ فاتورة ${invoice.invoiceNumber}',
        newValues: {
          'invoiceNumber': invoice.invoiceNumber,
          'filePath': filePath,
          'saveDate': DateTime.now().toIso8601String(),
        },
        description: 'تم حفظ الفاتورة كملف PDF',
        category: 'InvoicePrint',
      );

      LoggingService.info(
        'تم حفظ الفاتورة كـ PDF بنجاح',
        category: 'InvoicePrint',
        data: {'filePath': filePath},
      );

      return filePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في حفظ الفاتورة كـ PDF',
        category: 'InvoicePrint',
        data: {'error': e.toString(), 'invoiceId': invoice.id},
      );
      return null;
    }
  }

  /// إنشاء PDF للفاتورة
  static Future<pw.Document> _generateInvoicePDF(Invoice invoice) async {
    final pdf = pw.Document();

    // جلب بيانات العميل/المورد
    Customer? customer;
    Supplier? supplier;
    
    if (invoice.customerId != null) {
      customer = await _customerService.getCustomerById(invoice.customerId!);
    }
    if (invoice.supplierId != null) {
      supplier = await _supplierService.getSupplierById(invoice.supplierId!);
    }

    // جلب بيانات الأصناف
    final List<Map<String, dynamic>> itemsWithDetails = [];
    for (final invoiceItem in invoice.items) {
      final item = await _itemService.getItemById(invoiceItem.itemId);
      itemsWithDetails.add({
        'invoiceItem': invoiceItem,
        'item': item,
      });
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildInvoiceHeader(invoice),
        footer: (context) => _buildInvoiceFooter(context),
        build: (context) => [
          // معلومات الشركة والعميل/المورد
          _buildCompanyAndClientInfo(invoice, customer, supplier),
          pw.SizedBox(height: 20),

          // جدول الأصناف
          _buildItemsTable(itemsWithDetails),
          pw.SizedBox(height: 20),

          // ملخص الفاتورة
          _buildInvoiceSummary(invoice),
          pw.SizedBox(height: 20),

          // الشروط والملاحظات
          if (invoice.terms != null || invoice.notes != null)
            _buildTermsAndNotes(invoice),
        ],
      ),
    );

    return pdf;
  }

  /// بناء رأس الفاتورة
  static pw.Widget _buildInvoiceHeader(Invoice invoice) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(width: 2, color: PdfColors.blue),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // معلومات الشركة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                _companyName,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(_companyAddress, style: const pw.TextStyle(fontSize: 12)),
              pw.Text(_companyPhone, style: const pw.TextStyle(fontSize: 12)),
              pw.Text(_companyEmail, style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
          // معلومات الفاتورة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                invoice.typeArabic,
                style: pw.TextStyle(
                  fontSize: 20,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
              pw.Text('التاريخ: ${DateFormat('yyyy/MM/dd').format(invoice.invoiceDate)}'),
              if (invoice.dueDate != null)
                pw.Text('تاريخ الاستحقاق: ${DateFormat('yyyy/MM/dd').format(invoice.dueDate!)}'),
              pw.Text('الحالة: ${invoice.statusArabic}'),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة
  static pw.Widget _buildInvoiceFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Text(
        'صفحة ${context.pageNumber} من ${context.pagesCount}',
        style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey),
      ),
    );
  }

  /// بناء معلومات الشركة والعميل/المورد
  static pw.Widget _buildCompanyAndClientInfo(
    Invoice invoice,
    Customer? customer,
    Supplier? supplier,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // معلومات العميل/المورد
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  customer != null ? 'بيانات العميل' : 'بيانات المورد',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue,
                  ),
                ),
                pw.SizedBox(height: 10),
                if (customer != null) ...[
                  pw.Text('الاسم: ${customer.name}'),
                  if (customer.phone.isNotEmpty) pw.Text('الهاتف: ${customer.phone}'),
                  if (customer.email.isNotEmpty) pw.Text('البريد: ${customer.email}'),
                  if (customer.address.isNotEmpty) pw.Text('العنوان: ${customer.address}'),
                ] else if (supplier != null) ...[
                  pw.Text('الاسم: ${supplier.name}'),
                  if (supplier.phone.isNotEmpty) pw.Text('الهاتف: ${supplier.phone}'),
                  if (supplier.email.isNotEmpty) pw.Text('البريد: ${supplier.email}'),
                  if (supplier.address.isNotEmpty) pw.Text('العنوان: ${supplier.address}'),
                ] else ...[
                  pw.Text('لا توجد بيانات متاحة'),
                ],
              ],
            ),
          ),
        ),
        pw.SizedBox(width: 20),
        // معلومات إضافية
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'معلومات إضافية',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue,
                  ),
                ),
                pw.SizedBox(height: 10),
                if (invoice.reference != null)
                  pw.Text('المرجع: ${invoice.reference}'),
                pw.Text('تاريخ الإنشاء: ${DateFormat('yyyy/MM/dd HH:mm').format(invoice.createdAt)}'),
                if (invoice.paidAmount > 0)
                  pw.Text('المبلغ المدفوع: ${invoice.paidAmount.toStringAsFixed(2)}'),
                if (invoice.remainingAmount > 0)
                  pw.Text('المبلغ المتبقي: ${invoice.remainingAmount.toStringAsFixed(2)}'),
              ],
            ),
          ),
        ),
      ],
    );
  }
